<?php
BrandAPI::on_load();

class BrandAPI
{
    private static $url;
    private static $brandEndpoint;
    private static $footerLogosEndpoint;
    private static $configurationEndpoint;
    private static $loginDepositMethodsEndpoint;
    private static $textKeysEndpoint;
    private static $navigationEndpoint;
    private static $transientStorageTime;

    static function on_load()
    {
        add_action('init', [self::class, 'init']);
    }

    static function init()
    {
        new BrandAPI;

        // Set up cron job for navigation data fetching
        self::setupNavigationCron();
    }

    function __construct()
    {
        global $settingsOverAPI, $footerLogosOverAPI, $configOverAPI, $loginDepositMethodsOverAPI, $navigationOverAPI;

        self::$url                   = brandUrl();
        self::$brandEndpoint         = '/rest/state/brand/';
        self::$configurationEndpoint = '/rest/state/configuration';
        self::$footerLogosEndpoint   = '/rest/footer/logo';
        self::$navigationEndpoint    = '/rest/navigation';
        self::$textKeysEndpoint      = '/textKey';
        self::$transientStorageTime  = (24 * 60 * 60) * 7;     // Data will be cached for 7 days
        self::$loginDepositMethodsEndpoint = '/PaymentFormDataService/getLoginDepositPaymentMethods';

        if(SITE_TYPE != SITE_TYPE_SEO) {
            $settingsOverAPI    = static::getBrandSettings();   // bonusTerms, footerLinks
            $footerLogosOverAPI = static::getFooterLogos();
            $configOverAPI      = static::getConfigurations();
            $loginDepositMethodsOverAPI = static::getLoginDepositMethods();
            $navigationOverAPI = static::getNavigation();
        }
    }

    private static function getTextKeysFromFooterLinks($footerLinks, $transientData)
    {
        foreach ($footerLinks as $responseGroup) {
            foreach ($transientData['footerLinks'] as $itemGroup) {
                if (!is_array($itemGroup)) continue;
                foreach ($itemGroup as $linkGroup) {
                    if (!is_array($linkGroup)) continue;
                    foreach ($linkGroup as $link) {
                        if (!empty($link['text']) && !empty($transientData['footerLinks'])) {
                            if (!array_key_exists('textKeys', $transientData['footerLinks'])) {
                                $transientData['footerLinks']['textKeys'] = [];
                            }
                            if (!in_array($link['text'], $transientData['footerLinks']['textKeys'])) {
                                array_push($transientData['footerLinks']['textKeys'], $link['text']);
                            }
                        }
                    }
                }
            }
        }
        return $transientData;
    }

    private static function addDomainIfNotExistsInUrl($footerLinks, $transientData)
    {
        foreach ($footerLinks as $responseGroup) {
            foreach ($transientData['footerLinks'] as $itemGroup) {
                if (!is_array($itemGroup)) continue;
                foreach ($itemGroup as $linkGroup) {
                    if (!is_array($linkGroup)) continue;
                    foreach ($linkGroup as $link) {
                        if (!empty($link['url']) && !empty($transientData['footerLinks'])) {
                            // Check if url contains a domain that starts with http or https
                            if (!str_contains($link['url'], 'http')) {
                                // Add slash if first char is not already slash
                                if ($link['url'][0] !== "/") {
                                    $link['url'] .= "/";
                                }
                                /*
                                    Add brand domain in the beginning of url
                                    to convert any url like "/about" into "https://www.comeon.com/about"
                                */
                                global $apiBase;
                                $link['url'] = $apiBase[SITE_TYPE_PRODUCTION] . $link['url'];
                            }
                        }
                    }
                }
            }
        }
        return $transientData;
    }

    private static function getValuesForTextKeys($responseFromTextKeysAPI, $transientData, $propName = '')
    {
        if (
            !empty($responseFromTextKeysAPI['status']) &&
            $responseFromTextKeysAPI['status'] === "SUCCESS"
        ) {
            unset($transientData[$propName]['textKeys']);

            if ($responseFromTextKeysAPI['result']) {
                foreach ($responseFromTextKeysAPI['result'] as $textKey => $textValue) {
                    $textKeyAndValue = ['textKey' => $textKey, 'textValue' => $textValue];

                    array_walk_recursive(
                        $transientData[$propName],
                        function (&$value, $args, $textKeyAndValue) {
                            if (is_string($value)) {
                                if ($value === $textKeyAndValue['textKey']) {
                                    $value = $textKeyAndValue['textValue'];
                                }
                                if ($value === 'NEWSLETTER_SENDER_NAME_CASINO') {
                                    $value = ucfirst(CURRENT_BRAND);
                                }
                            }
                        },
                        $textKeyAndValue
                    );
                }
            }
        }
        return $transientData;
    }

    /**
     * Set up cron job for navigation data fetching
     */
    private static function setupNavigationCron()
    {
        // Add custom cron schedule for every 4 hours if not already exists
        add_filter('cron_schedules', function($schedules) {
            if (!isset($schedules['every_four_hours'])) {
                $schedules['every_four_hours'] = [
                    'interval' => 4 * 60 * 60, // 4 hours in seconds
                    'display' => __('Every 4 Hours', 'phoenix')
                ];
            }
            return $schedules;
        });

        // Schedule the cron job if not already scheduled
        $cron_hook = 'px_fetch_navigation_data';
        if (!wp_next_scheduled($cron_hook)) {
            wp_schedule_event(time(), 'every_four_hours', $cron_hook);
        }

        // Hook the function to the cron event
        add_action($cron_hook, [self::class, 'fetchNavigationDataViaCron']);
    }

    /**
     * Fetch navigation data via cron job and store in permanent transient
     */
    public static function fetchNavigationDataViaCron()
    {
        // Add franchise code to the endpoint if not already added
        if(!str_contains(self::$navigationEndpoint, 'franchiseCode')) {
            $franchiseCode = self::getFranchiseCode();
            if (!empty($franchiseCode)) {
                self::$navigationEndpoint = self::$navigationEndpoint . "?franchiseCode=" . $franchiseCode;
            }
        }

        $api = new Api(self::$url . self::$navigationEndpoint);
        $response = $api->get();
        $navigationData = [
            'primaryNavigation' => [],
            'secondaryNavigation' => []
        ];

        if ($response) {
            if (is_array($response) && array_key_exists('result', $response)) {
                if (is_string($response['result'])) {
                    $response['result'] = json_decode($response['result'], true);
                }

                if (!empty($response['result']['primaryNavigation'])) {
                    $navigationData['primaryNavigation'] = self::processNavigationUrls($response['result']['primaryNavigation']);
                }

                /*
                    Commented out to skip submenu items coming via API,
                    so there won't be any submenu links
                */
                // if (!empty($response['result']['secondaryNavigation'])) {
                //     $navigationData['secondaryNavigation'] = [];
                //     foreach ($response['result']['secondaryNavigation'] as $key => $items) {
                //         $navigationData['secondaryNavigation'][$key] = self::processNavigationUrls($items);
                //     }
                // }
            }
        }

        // Store in permanent transient (no expiration)
        set_transient('px_api_navigation_' . CURRENT_REGION, $navigationData, 0);

        if(!empty($navigationData['primaryNavigation'])) {
            // Create and set WordPress menu for mobile-menu location
            self::createWordPressMenuFromNavigation($navigationData);
        }
    }

    private static function getFranchiseCode()
    {
        $allFranchiseCodes       = [
            "888"            => [
                "en"    => "NETHERLANDS_888", // FALLBACK
                "nl"    => "NETHERLANDS_888" // KSA
            ],
            "casinostugan"   => [
                "en" => "SWEDEN_CASINOSTUGAN", // FALLBACK
                "sv" => "SWEDEN_CASINOSTUGAN", // SGA
            ],
            "casinostuen"    => [
                "en" => "DENMARK_CASINOSTUEN", // FALLBACK
                "dk" => "DENMARK_CASINOSTUEN", // DGA
            ],
            "cherrycasino"   => [
                "en"     => "GLOBAL_CHERRYCASINO", // GLOBAL
                "sv"     => "SWEDEN_CHERRYCASINO", // SWEDEN
                "no"     => "NORWAY_CHERRYCASINO", // MGA | TODO: Possible replacement as GLOBAL_CHERRYCASINO after Norway 2.0
            ],
            "comeon"         => [
                "en"     => "GLOBAL_COMEON", // GLOBAL
                "ca"     => "CANADA_COMEON", // MGA
                "on"     => "CANADA_ONTARIO_COMEON", // AGCO
                "dk"     => "DENMARK_COMEON", // DGA
                "da"     => "DENMARK_COMEON", // DGA
                "fi"     => "FINLAND_COMEON", // MGA
                "no"     => "NORWAY_COMEON", // MGA | TODO: Possible replacement as GLOBAL_COMEON after Norway 2.0
                "pl"     => "POLAND_COMEON", // PGA
                "sv"     => "SWEDEN_COMEON", // MGA
                "nl"     => "NETHERLANDS_COMEON" // KSA
            ],
            "euroslots" => [
                "en" => "FINLAND_EUROSLOTS", // FALLBACK
                "fi" => "FINLAND_EUROSLOTS" // MGA
            ],
            "folkeriket"     => [
                "en"     => "GLOBAL_FOLKEAUTOMATEN", // GLOBAL
                "no"     => "NORWAY_FOLKEAUTOMATEN", // MGA | TODO: Possible replacement as GLOBAL_FOLKEAUTOMATEN after Norway 2.0
            ],
            "galaksino"      => [
                "en" => "ENGLISH_GALAKSINO", // ENGLISH
                "fi" => "FINLAND_GALAKSINO", // MGA
            ],
            "getlucky"       => [
                "en"     => "GLOBAL_GETLUCKY", // GLOBAL
                "dk"     => "DENMARK_GETLUCKY", // DGA
                "da"     => "DENMARK_GETLUCKY", // DGA
                "fi"     => "FINLAND_GETLUCKY_GUEST", // MGA
                "no"     => "NORWAY_GETLUCKY", // MGA | TODO: Possible replacement as GLOBAL_GETLUCKY after Norway 2.0
            ],
            "hajper"         => [
                "en" => "ENGLISH_HAJPER", // ENGLISH
                "sv" => "SWEDEN_HAJPER", // SGA
            ],
            "lyllo"          => [
                "en" => "SWEDEN_LYLLO", // FALLBACK
                "sv" => "SWEDEN_LYLLO", // SGA
            ],
            "mobilautomaten" => [
                "en"     => "GLOBAL_MOBILAUTOMATEN", // GLOBAL
                "no"     => "NORWAY_MOBILAUTOMATEN", // MGA | TODO: Possible replacement as GLOBAL_MOBILAUTOMATEN after Norway 2.0
            ],
            "mobilebet"      => [
                "en"       => "GLOBAL_MOBILEBET", // GLOBAL
                "de"       => "GERMANY_MOBILEBET", // GGA
                "fi"       => "FINLAND_MOBILEBET", // MGA
                "no"       => "NORWAY_MOBILEBET", // MGA | TODO: Possible replacement as GLOBAL_MOBILEBET after Norway 2.0
            ],
            "nopeampi"       => [
                "en" => "ENGLISH_NOPEAMPI", // ENGLISH
                "fi" => "FINLAND_NOPEAMPI", // MGA
            ],
            "norgesspill"    => [
                "en"     => "GLOBAL_NORGESSPILL", // GLOBAL
                "no"     => "NORWAY_NORGESSPILL", // MGA | TODO: Possible replacement as GLOBAL_NORGESSPILL after Norway 2.0
            ],
            "pzbuk"          => [
                "en" => "GLOBAL_PZBUK", // FALLBACK
                "pl" => "POLAND_PZBUK", // PGA
            ],
            "snabbare"       => [
                "en" => "ENGLISH_SNABBARE", // ENGLISH
                "sv" => "SWEDEN_SNABBARE", // SGA
            ],
            "sunmaker"       => [
                "en" => "GLOBAL_SUNMAKER", // FALLBACK
                "de" => "GERMANY_SUNMAKER", // GGA
            ],
            "suomikasino"    => [
                "en" => "ENGLISH_SUOMIKASINO", // ENGLISH
                "fi" => "FINLAND_SUOMIKASINO" // MGA
            ],
            "spinon" => [
                "en" => "GLOBAL_SPINON", // FALLBACK
            ]
        ];
        if (!empty($allFranchiseCodes[CURRENT_BRAND][CURRENT_REGION])) {
            return $allFranchiseCodes[CURRENT_BRAND][CURRENT_REGION];
        }

        return $allFranchiseCodes[CURRENT_BRAND]["en"];
    }

    private static function getValuesForKeyFromAPIResponse($key, $haystack)
    {
        $result = [];
        if (array_key_exists($key, $haystack)) {
            if (!empty($haystack[$key])) {
                $result = $haystack[$key];
            }
        }
        return $result;
    }

    public static function getBrandSettings()
    {
        // do_action('qm/debug', 'BrandAPI ENABLED');
        $response = get_transient('px_api_brand_' . CURRENT_REGION);

        // If cached data not found, fetch Brand API and cache
        if (!($response) || $response === NULL) {
            $api           = new Api(self::$url  . self::$brandEndpoint);
            $response      = $api->get();
            $transientData = [
                // 'bonusTerms'  => '',
                'footerLinks' => []
            ];
            if ($response) {
                if (is_array($response)) {
                    if (array_key_exists('result', $response)) {
                        if (is_string($response['result'])) {
                            $response['result'] = json_decode($response['result'], true);
                        }

                        $transientData = array_merge($response['result'], $transientData);

                        // Getting footer links from API
                        if (!empty($response['result']['footerLinks'])) {
                            $transientData['footerLinks'] = json_decode($response['result']['footerLinks'], true);

                            // Extract textKeys from footerLinks (this method fills $transientData['footerLinks']['textKeys'])
                            $transientData = self::getTextKeysFromFooterLinks($transientData['footerLinks'], $transientData);

                            // Add brand domain in links if not already exists
                            $transientData = self::addDomainIfNotExistsInUrl($transientData['footerLinks'], $transientData);

                            // Fetch values for textKeys
                            if (!empty($transientData['footerLinks']['textKeys'])) {
                                $api           = new Api(self::$url  . self::$textKeysEndpoint);
                                $response      = $api->post(json_encode($transientData['footerLinks']['textKeys']));

                                // Assign values to related textKeys
                                $transientData = self::getValuesForTextKeys($response, $transientData, 'footerLinks');
                            }
                        }

                        // if($response && is_array($response) && !empty($response['result'])) {
                        //     // Bonus terms from API
                        //     $transientData['bonusTerms'] = $response['result']['bonusTerms'];
                        // }

                        // TODO: Footer description text
                        // $transientData['footerDescription'] = self::getValuesForKeyFromAPIResponse('footerDescription', $response['result']);

                        // TODO: Language switches
                        // $transientData['languages'] = self::getValuesForKeyFromAPIResponse('languages', $response['result']);

                        // TODO: Social media links
                        // $transientData['socialMediaLinks'] = self::getValuesForKeyFromAPIResponse('socialMediaLinks', $response['result']);

                        // TODO: Check guides text
                        // $transientData['checkGuidesText'] = self::getValuesForKeyFromAPIResponse('checkGuidesText', $response['result']);

                        // TODO: Disclaimer text
                        // $transientData['disclaimerText'] = self::getValuesForKeyFromAPIResponse('disclaimerText', $response['result']);

                        // TODO: Disclaimer logo
                        // $transientData['disclaimerLogo'] = self::getValuesForKeyFromAPIResponse('disclaimerLogo', $response['result']);

                        // TODO: Payment method logos
                        // $transientData['paymentMethodLogos'] = self::getValuesForKeyFromAPIResponse('paymentMethodLogos', $response['result']);

                        // TODO: Compliance logos
                        // $transientData['complianceLogos'] = self::getValuesForKeyFromAPIResponse('complianceLogos', $response['result']);

                    }
                }
            }

            set_transient('px_api_brand_' . CURRENT_REGION, $transientData, self::$transientStorageTime);
            $response = $transientData;
        }
        return $response;
    }

    public static function getFooterLogos()
    {
        // Check if franchiseCode is already appended to endpoint
        if(!str_contains(self::$footerLogosEndpoint, 'franchiseCode')) {
            // Get franchise code and append to endpoint
            $franchiseCode = self::getFranchiseCode();
            if (!empty($franchiseCode)) {
                self::$footerLogosEndpoint = self::$footerLogosEndpoint . "?franchiseCode=" . $franchiseCode;
            }
        }

        $response = get_transient('px_api_brand_footer_logos_' . CURRENT_REGION);

        // If cached data not found, fetch fresh data
        if (!($response) || $response === NULL) {
            $api           = new Api(self::$url  . self::$footerLogosEndpoint);
            $response      = $api->get();

            // Fallback setter to get rid of php warnings undefined key/var etc.
            $transientData = [
                'payments' => [],
                'flags' => [],
                'others' => [],
            ];

            if ($response) {
                if (is_array($response)) {
                    if (array_key_exists('status', $response)) {
                        if ($response['status'] === "SUCCESS") {
                            if (array_key_exists('result', $response)) {

                                if (array_key_exists('SVGBasePath', $response['result'])) {
                                    $basePath = $response['result']['SVGBasePath'];
                                }
                                if (array_key_exists('CDN_URL', $response['result'])) {
                                    $cdn = $response['result']['CDN_URL'];
                                }

                                // Parse logos and set image urls with concetanation of CDN and path
                                foreach ($transientData as $key => $value) {
                                    if (array_key_exists($key, $response['result'])) {
                                        $transientData[$key] = $response['result'][$key];

                                        foreach ($transientData[$key] as $logoIndex => $logoValue) {
                                            if (array_key_exists('imageURL', $transientData[$key][$logoIndex])) {
                                                $transientData[$key][$logoIndex]['imageURL'] = $cdn . $basePath . $transientData[$key][$logoIndex]['imageURL'];
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            set_transient('px_api_brand_footer_logos_' . CURRENT_REGION, $transientData, self::$transientStorageTime);
            $response = $transientData;
        }

        return $response;
    }

    public static function getConfigurations()
    {
        $response = get_transient('px_api_configuration_' . CURRENT_REGION);

        // If cached data not found, fetch fresh data
        if (!($response) || $response === NULL) {

            // Check if franchiseCode is already appended to endpoint
            if(!str_contains(self::$configurationEndpoint, 'franchiseCode')) {
                $franchiseCode = self::getFranchiseCode();
                // Get franchise code and append to endpoint
                if(!empty($franchiseCode)) {
                    self::$configurationEndpoint = self::$configurationEndpoint . "?franchiseCode=" . $franchiseCode;
                }
            }

            // Get config via API
            $api           = new Api(self::$url  . self::$configurationEndpoint);
            $response      = $api->get();

            if ($response) {
                if (is_array($response)) {
                    if (array_key_exists('result', $response)) {
                        if (is_string($response['result'])) {
                            $response['result'] = json_decode($response['result'], true);
                        }

                        $transientData = $response['result'];
                        $response      = $transientData;
                        set_transient('px_api_configuration_' . CURRENT_REGION, $transientData, (15 * 60)); // store for 15 minutes in transient as a cache layer
                    }
                }
            }
        }

        return $response;
    }

    public static function getLoginDepositMethods()
    {
        // Commented payment methods will be excluded from response, therefore not displayed on any page
        $textKeyAndFileNameMapList = [
            // "MOBILE_PAYMENT_METHOD_BANK_TRANSFER"              => "Direct bank transfer",
            "MOBILE_PAYMENT_METHOD_SWISH"                      => "swish",
            "MOBILE_PAYMENT_METHOD_VISA"                       => "visa",
            "PAYMENT_METHOD_ADYEN_IDEAL"                       => "ideal",
            "PAYMENT_METHOD_ADYEN_MOBILEPAY"                   => "mobilepay",
            "PAYMENT_METHOD_ADYEN_SWISH"                       => "swish",
            "PAYMENT_METHOD_AKONTO_PAY_AIRTEL_MONEY"           => "airtel",
            "PAYMENT_METHOD_AKONTO_PAY_JIO"                    => "jio",
            "PAYMENT_METHOD_AKONTO_PAY_MOBIKWIK"               => "mobikwik",
            "PAYMENT_METHOD_AKONTO_PAY_NETBANKING"             => "netbanking",
            "PAYMENT_METHOD_AKONTO_PAY_PAYTM"                  => "paytm",
            "PAYMENT_METHOD_AKONTO_PAY_PHONEPE"                => "phonepe",
            "PAYMENT_METHOD_AKONTO_PAY_UPI"                    => "upi",
            "PAYMENT_METHOD_APPLE_PAY"                         => "applepay",
            "PAYMENT_METHOD_ASTROPAY"                          => "astropay",
            "PAYMENT_METHOD_ASTROPAYCARD"                      => "astropay",
            // "PAYMENT_METHOD_AZONDO"                            => "Card via Crypto",
            // "PAYMENT_METHOD_BANCO_SANTANDER"                   => "Banco Santander",
            // "PAYMENT_METHOD_BANK_WIRE"                         => "Bank Transfer",
            // "PAYMENT_METHOD_BANK_WIRE_INDIA"                   => "Bank Transfer - Gold & Silver Coins",
            "PAYMENT_METHOD_BANK_WIRE_INDIA_UPI"               => "upi",
            // "PAYMENT_METHOD_BILDERLINGS"                       => "Bankoverføring",
            "PAYMENT_METHOD_BITCOIN"                           => "bitcoin",
            "PAYMENT_METHOD_BLIK"                              => "blik",
            // "PAYMENT_METHOD_BPAY"                              => "bPay",
            "PAYMENT_METHOD_BRITE"                             => "brite",
            // "PAYMENT_METHOD_CGIFT"                             => "Bankkort via Krypto",
            // "PAYMENT_METHOD_CLICK_2_PAY"                       => "Click2Pay",
            // "PAYMENT_METHOD_COLLEXO"                           => "Bankkort via Quantum",
            // "PAYMENT_METHOD_CONNECTPAY"                        => "Bank Overføring",
            "PAYMENT_METHOD_CONSTANT_PAY_NETBANKING"           => "netbanking",
            "PAYMENT_METHOD_CONSTANT_PAY_PAYTM"                => "paytm",
            "PAYMENT_METHOD_CONSTANT_PAY_PHONEPE"              => "phonepe",
            "PAYMENT_METHOD_CONSTANT_PAY_UPI"                  => "upi",
            "PAYMENT_METHOD_CREDIT_CARD"                       => "creditcard",
            // "PAYMENT_METHOD_CREDORAX_POLI"                     => "POLi",
            // "PAYMENT_METHOD_DINEROMAIL"                        => "DineroMail",
            "PAYMENT_METHOD_DIRECTA24_AIRTEL"                  => "airtel",
            "PAYMENT_METHOD_DIRECTA24_JIO"                     => "jio",
            "PAYMENT_METHOD_DIRECTA24_MOBIKWIK"                => "mobikwik",
            "PAYMENT_METHOD_DIRECTA24_NETBANKING"              => "netbanking",
            "PAYMENT_METHOD_DIRECTA24_PAYTM"                   => "paytm",
            "PAYMENT_METHOD_DIRECTA24_PHONEPE"                 => "phonepe",
            "PAYMENT_METHOD_DIRECTA24_UPI"                     => "upi",
            // "PAYMENT_METHOD_DOSHPAY"                           => "Bankoverføring",
            "PAYMENT_METHOD_DOTPAY"                            => "dotpay",
            // "PAYMENT_METHOD_DOTPAY_GOOGLEPAY"                  => "GooglePay",
            "PAYMENT_METHOD_DOTPAY_PIQ"                        => "blik",
            // "PAYMENT_METHOD_DOTPAY_PIQ_BANK"                   => "Local Bank Payouts",
            "PAYMENT_METHOD_DOTPAY_PIQ_BLIK"                   => "blik",
            // "PAYMENT_METHOD_ECARD"                             => "Ecard",
            // "PAYMENT_METHOD_ECOBANQ"                           => "Ecobanq",
            "PAYMENT_METHOD_ECOPAYZ"                           => "ecopayz",
            // "PAYMENT_METHOD_EMONEY"                            => "Bankoverføring",
            // "PAYMENT_METHOD_EMP"                               => "Bankoverføring",
            // "PAYMENT_METHOD_EMP_CASHLIB"                       => "Cashlib Kuponger",
            "PAYMENT_METHOD_EMP_CG"                            => "visa",
            // "PAYMENT_METHOD_EMP_KWICKGO"                       => "KwickGo - Mastercard",
            // "PAYMENT_METHOD_EMP_KWICKNGO"                      => "Mastercard via Kuponger",
            "PAYMENT_METHOD_EMP_PASSNGO"                       => "visa",
            // "PAYMENT_METHOD_ENTERCASH"                         => "Entercash",
            "PAYMENT_METHOD_ENTROPAY"                          => "entropay",
            // "PAYMENT_METHOD_ENVOY_BANKOUT_CANADA_DOMESTIC"     => "Bank Wire Canada",
            "PAYMENT_METHOD_ENVOY_GIROPAY"                     => "giropay",
            "PAYMENT_METHOD_ENVOY_SOFORT"                      => "sofort",
            // "PAYMENT_METHOD_EPS"                               => "EPS",
            "PAYMENT_METHOD_EUTELLER"                          => "euteller",
            // "PAYMENT_METHOD_EWIRE_DK"                          => "ewire_dk",
            // "PAYMENT_METHOD_EWIRE_NO"                          => "ewire_no",
            // "PAYMENT_METHOD_EWIRE_SE"                          => "ewire_se",
            "PAYMENT_METHOD_EZEE_WALLET"                       => "ezeewallet",
            // "PAYMENT_METHOD_FAST_BANK_TRANSFER"                => "Fast Bank Transfer",
            // "PAYMENT_METHOD_FINDUCTIVE"                        => "Bankoverføring",
            // "PAYMENT_METHOD_FLYKK"                             => "Flykk",
            "PAYMENT_METHOD_FOGHORN_INTERAC"                   => "interac",
            // "PAYMENT_METHOD_FUNDSEND"                          => "FundSend",
            // "PAYMENT_METHOD_GENIWALLET"                        => "ETH - Ethereum",
            // "PAYMENT_METHOD_GENOME"                            => "Bank Overføring",
            "PAYMENT_METHOD_GIGADAT_INTERAC"                   => "interac",
            "PAYMENT_METHOD_GIGADAT_INTERAC_ACH"               => "ecashout",
            // "PAYMENT_METHOD_GIGADAT_INTERAC_WITHDRAW"          => "Interac e-Transfer",
            "PAYMENT_METHOD_GLUEPAY"                           => "trustly",
            // "PAYMENT_METHOD_GLUEPAY_DIRECT_DEBIT"              => "Trustly One Click",
            "PAYMENT_METHOD_HEXOPAY"                           => "visabyvoucher.",
            // "PAYMENT_METHOD_HEXOPAY_BILLSPAY"                  => "Bankoverføring",
            // "PAYMENT_METHOD_IDEAL"                             => "Instant Bank Transfer",
            "PAYMENT_METHOD_IMOJE_PBL"                         => "imoje",
            // "PAYMENT_METHOD_INPAY"                             => "Bank Transfer",
            // "PAYMENT_METHOD_INPAY_CANADA"                      => "Bank Transfer",
            // "PAYMENT_METHOD_INPAY_GERMANY"                     => "Bank Transfer",
            // "PAYMENT_METHOD_INPAY_JP"                          => "銀行送金",
            "PAYMENT_METHOD_INSTADEBIT"                        => "instadebit",
            // "PAYMENT_METHOD_ISX"                               => "Bankoverføring",
            "PAYMENT_METHOD_IWALLET"                           => "iwallet",
            "PAYMENT_METHOD_JETON"                             => "JetonWallet",
            // "PAYMENT_METHOD_JETONCASH"                         => "JetonCash",
            "PAYMENT_METHOD_JETONGO"                           => "jetongo",
            // "PAYMENT_METHOD_JPAY"                              => "銀行送金（J-Pay)",
            "PAYMENT_METHOD_KLUWP"                             => "visabyvoucher",
            // "PAYMENT_METHOD_KRIITA"                            => "Paylevo",
            // "PAYMENT_METHOD_LOBANET"                           => "Lobanet",
            // "PAYMENT_METHOD_LUQAPAY_BT_VOUCHER"                => "Voucher Via Bank",
            // "PAYMENT_METHOD_LUQAPAY_JAPAN"                     => "",
            "PAYMENT_METHOD_LUQAPAY_NETBANKING"                => "netbanking",
            // "PAYMENT_METHOD_LUQAPAY_SEPA"                      => "Bankoverføring",
            "PAYMENT_METHOD_LUQAPAY_UPI"                       => "upi",
            "PAYMENT_METHOD_LUXON"                             => "luxon",
            "PAYMENT_METHOD_MAGNIUS"                           => "revolut",
            // "PAYMENT_METHOD_MAGNIUS_CC"                        => "Revolut Cards",
            "PAYMENT_METHOD_MIFINITY"                          => "mifinity",
            // "PAYMENT_METHOD_MONETA"                            => "Moneta",
            // "PAYMENT_METHOD_MONEYBITE"                         => "Crypto - BTC",
            // "PAYMENT_METHOD_MONEYBITE_ETH"                     => "Crypto - ETH",
            // "PAYMENT_METHOD_MONEYBITE_USDT"                    => "Crypto - USDT (TRC20)",
            "PAYMENT_METHOD_MONEYBOOKERS"                      => "skrill",
            "PAYMENT_METHOD_MONEYBOOKERS_1TAP"                 => "skrill1tap",
            // "PAYMENT_METHOD_MONEYBOOKERS_DANKORT"              => "Dankort by Skrill",
            // "PAYMENT_METHOD_MONEYBOOKERS_DINERS"               => "Diners by Skrill",
            "PAYMENT_METHOD_MONEYBOOKERS_ONLINE_BANK_TRANSFER" => "rapidtransfer",
            "PAYMENT_METHOD_MUCHBETTER"                        => "much-better",
            "PAYMENT_METHOD_NEOSURF"                           => "neosurf",
            "PAYMENT_METHOD_NETELLER"                          => "neteller",
            // "PAYMENT_METHOD_NUVEI_BANK_TRANSFER"               => "Nuvei",
            // "PAYMENT_METHOD_OMNIMATRIX"                        => "Frontnode - krypto via kort",
            "PAYMENT_METHOD_OMNIMATRIX_JP"                     => "visa",
            // "PAYMENT_METHOD_OPENPAYD"                          => "Bank-Overføring",
            "PAYMENT_METHOD_ORIENTAL_WALLET"                   => "orientalwallet",
            "PAYMENT_METHOD_P24"                               => "przelewy",
            "PAYMENT_METHOD_PARAMOUNT_IDEBIT"                  => "idebit",
            "PAYMENT_METHOD_PARAMOUNT_INSTADEBIT"              => "instadebit",
            "PAYMENT_METHOD_PARAMOUNT_INTERAC"                 => "interac",
            "PAYMENT_METHOD_PAY_FUTURE"                        => "netbanking",
            "PAYMENT_METHOD_PAY_FUTURE_UPI"                    => "upi",
            // "PAYMENT_METHOD_PAY_FUTURE_WITHDRAW"               => "स्थानीय बैंक ट्रांसफर",
            // "PAYMENT_METHOD_PAY_FUTURE_WITHDRAW"               => "Local Bank Transfer",
            // "PAYMENT_METHOD_PAY_FUTURE_WITHDRAWAL"             => "Local Bank Transfer",
            "PAYMENT_METHOD_PAY_LOGIC_NETBANKING"              => "netbanking",
            "PAYMENT_METHOD_PAY_LOGIC_PHONEPE"                 => "phonepe",
            "PAYMENT_METHOD_PAY_LOGIC_UPI"                     => "upi",
            "PAYMENT_METHOD_PAY_SAFE_CARD"                     => "paysafe",
            // "PAYMENT_METHOD_PAYDO"                             => "Paydo Wallet",
            "PAYMENT_METHOD_PAYFUTURE_NETBANKING"              => "netbanking",
            "PAYMENT_METHOD_PAYFUTURE_UPI"                     => "upi",
            "PAYMENT_METHOD_PAYGENICS_NETBANKING"              => "netbanking",
            "PAYMENT_METHOD_PAYGENICS_UPI"                     => "upi",
            // "PAYMENT_METHOD_PAYGENICS_WALLETS"                 => "Phonepe / JIO & other wallets",
            "PAYMENT_METHOD_PAYMENTIQ_MOBILEPAY"               => "mobilepay",
            // "PAYMENT_METHOD_PAYMENTWORLD"                      => "Bankoverføring",
            "PAYMENT_METHOD_PAYMERO_AIRTEL_MONEY"              => "airtel",
            "PAYMENT_METHOD_PAYMERO_JIO"                       => "jio",
            "PAYMENT_METHOD_PAYMERO_MOBIKWIK"                  => "mobikwik",
            "PAYMENT_METHOD_PAYMERO_NET_BANKING"               => "netbanking",
            "PAYMENT_METHOD_PAYMERO_PAYTM"                     => "paytm",
            "PAYMENT_METHOD_PAYMERO_PHONEPE"                   => "phonepe",
            "PAYMENT_METHOD_PAYMERO_UPI"                       => "upi",
            // "PAYMENT_METHOD_PAYMERO_WALLET"                    => "Paymero Wallet",
            // "PAYMENT_METHOD_PAYORO"                            => "Bank Transfer",
            "PAYMENT_METHOD_PAYPAL"                            => "paypal",
            // "PAYMENT_METHOD_PAYPER_PAYBILT_INTERAC"            => "PAYPER_PAYBILT",
            "PAYMENT_METHOD_PAYSAFE_NETELLER"                  => "neteller",
            // "PAYMENT_METHOD_POLI"                              => "POLi",
            "PAYMENT_METHOD_PRZELEWY"                          => "przelewy",
            // "PAYMENT_METHOD_PUGGLEPAY"                         => "Direktbank",
            // "PAYMENT_METHOD_PUGGLEPAY"                         => "Zimpler", // Setting this as zimpler until a further notice
            "PAYMENT_METHOD_PURPLEPAY"                         => "visabyvoucher",
            // "PAYMENT_METHOD_QUICKBIT"                          => "Visa via Quickbit",
            "PAYMENT_METHOD_RUSH_DELUXE_NETBANKING"            => "netbanking",
            "PAYMENT_METHOD_RUSH_DELUXE_UPI"                   => "upi",
            // "PAYMENT_METHOD_RUSH_DELUXE_WALLET"                => "Mobile Wallets",
            "PAYMENT_METHOD_SAFEXPAY_NETBANKING"               => "netbanking",
            // "PAYMENT_METHOD_SAFEXPAY_NETBANKING_WITHDRAW"      => "Bank Withdrawal",
            "PAYMENT_METHOD_SAFEXPAY_UPI"                      => "upi",
            "PAYMENT_METHOD_SEC_JCB"                           => "jcb",
            "PAYMENT_METHOD_SEC_MAESTRO"                       => "maestro",
            "PAYMENT_METHOD_SEC_MASTERCARD"                    => "mastercard",
            // "PAYMENT_METHOD_SEC_SOLO"                          => "Solo",
            "PAYMENT_METHOD_SEC_VISA"                          => "visa",
            // "PAYMENT_METHOD_SEC_VISA_DEBIT_DELTA"              => "Visa Debit Delta",
            // "PAYMENT_METHOD_SEC_VISAELECTRON"                  => "Visa Electron",
            // "PAYMENT_METHOD_SERVIPAG_PAYMENT_BUTTON"           => "Servipag",
            "PAYMENT_METHOD_SHIFT4_APPLEPAY"                   => "applepay",
            // "PAYMENT_METHOD_SIFIPAY"                           => "Local Bank Withdrawal",
            "PAYMENT_METHOD_SIIRTO"                            => "siirto",
            "PAYMENT_METHOD_SIRU_MOBILE"                       => "siru",
            // "PAYMENT_METHOD_SMARTPAY"                          => "Smart Pay",
            "PAYMENT_METHOD_SMS_VOUCHER"                       => "sms-voucher",
            "PAYMENT_METHOD_SMSVOUCHER"                        => "sms-voucher",
            "PAYMENT_METHOD_SOFORT"                            => "sofort",
            // "PAYMENT_METHOD_SPEEDCARD"                         => "Speedcard",
            "PAYMENT_METHOD_STD_JCB"                           => "jcb",
            "PAYMENT_METHOD_STD_MAESTRO"                       => "maestro",
            "PAYMENT_METHOD_STD_MASTERCARD"                    => "mastercard",
            "PAYMENT_METHOD_STD_MASTERCARD_EPRO"               => "mastercard",
            // "PAYMENT_METHOD_STD_SOLO"                          => "Solo",
            "PAYMENT_METHOD_STD_VISA"                          => "visa",
            // "PAYMENT_METHOD_STD_VISA_DEBIT_DELTA"              => "Visa Debit Delta",
            "PAYMENT_METHOD_STD_VISA_EPRO"                     => "visa",
            // "PAYMENT_METHOD_STD_VISAELECTRON"                  => "Visa Electron",
            "PAYMENT_METHOD_SUMOPAY"                           => "sumopay",
            // "PAYMENT_METHOD_SUOMENV"                           => "Paytrail",
            "PAYMENT_METHOD_SWISH"                             => "swish",
            "PAYMENT_METHOD_TIGERPAY"                          => "tigerpay",
            "PAYMENT_METHOD_TPAY"                              => "tpay",
            "PAYMENT_METHOD_TPAY_BLIK"                         => "blik",
            "PAYMENT_METHOD_TRANSACTWORLD_MASTERCARD"          => "mastercard",
            // "PAYMENT_METHOD_TRANSACTWORLD_MC"                  => "MasterCard Debit",
            "PAYMENT_METHOD_TRANSACTWORLD_NETBANKING"          => "netbanking",
            // "PAYMENT_METHOD_TRANSACTWORLD_RUPAY"               => "Rupay Card",
            "PAYMENT_METHOD_TRANSACTWORLD_UPI"                 => "upi",
            // "PAYMENT_METHOD_TRANSACTWORLD_VISA"                => "Visa Debit",
            // "PAYMENT_METHOD_TRANSACTWORLD_WALLETS"             => "Phonepe / paytm",
            // "PAYMENT_METHOD_TRUELAYER_GERMANY"                 => "Truelayer",
            // "PAYMENT_METHOD_TRUELAYER_NETHERLANDS"             => "Truelayer",
            "PAYMENT_METHOD_TRUSTLY"                           => "trustly",
            "PAYMENT_METHOD_TRUSTLY_CANADA"                    => "trustly",
            // "PAYMENT_METHOD_TRUSTLY_CANADA_DIRECT_DEBIT"       => "Trustly One Click",
            "PAYMENT_METHOD_TRUSTLY_SWISH"                     => "swish",
            "PAYMENT_METHOD_TSI"                               => "visabyvoucher",
            // "PAYMENT_METHOD_TSI_VIP"                           => "TSI VIP channel",
            // "PAYMENT_METHOD_UKASH_VOUCHER"                     => "Ukash Voucher",
            // "PAYMENT_METHOD_UTORG"                             => "UTORG",
            // "PAYMENT_METHOD_VENUS_POINT"                       => "VenusPoint (ヴィーナスポイント)",
            // "PAYMENT_METHOD_VENUSPOINT"                        => "Vega Wallet",
            // "PAYMENT_METHOD_VIRTUAL_CREDIT_CARD"               => "Virtual Credit Card",
            "PAYMENT_METHOD_VISA"                              => "visa",
            // "PAYMENT_METHOD_WEBMONEY"                          => "WebMoney",
            // "PAYMENT_METHOD_ZIMPLER_BANK_WIRE"                 => "Banküberweisung",
            "PAYMENT_METHOD_ZIMPLER_SWISH"                     => "swish",
            "PAYMENT_METHOD_ZIMPLER_WALLET"                    => "zimpler",
            // "PAYMENT_METHOD_ZOTAPAY"                           => "銀行送金",
            // "PAYMENT_METHOD_ZOTAPAY_FREECHARGE"                => "Freecharge",
            "PAYMENT_METHOD_ZOTAPAY_JIO"                       => "jio",
            "PAYMENT_METHOD_ZOTAPAY_MOBIKWIK"                  => "mobikwik",
            "PAYMENT_METHOD_ZOTAPAY_NET_BANKING"               => "netbanking",
            "PAYMENT_METHOD_ZOTAPAY_PAYTM"                     => "paytm",
            "PAYMENT_METHOD_ZOTAPAY_PHONEPE"                   => "phonepe",
            "PAYMENT_METHOD_ZOTAPAY_UPI"                       => "upi",
        ];

        $response = get_transient('px_api_login_deposit_methods_' . CURRENT_REGION);

        // If cached data not found, fetch fresh data
        if (!($response) || $response === NULL) {

            // Add franchise code to the endpoint if not already added
            if(!str_contains(self::$loginDepositMethodsEndpoint, 'franchiseCode')) {
                $franchiseCode = self::getFranchiseCode();
                if (!empty($franchiseCode)) {
                    self::$loginDepositMethodsEndpoint = self::$loginDepositMethodsEndpoint . "?franchiseCode=" . $franchiseCode;
                }
            }

            // Get deposit methods for players not logged-in (login deposit methods) via API
            $api           = new Api(self::$url  . self::$loginDepositMethodsEndpoint);
            $response      = $api->get();

            if ($response) {
                if (is_array($response)) {
                    if (array_key_exists('loginPaymentMethodDataList', $response)) {
                        if (is_string($response['loginPaymentMethodDataList'])) {
                            $response['loginPaymentMethodDataList'] = json_decode($response['loginPaymentMethodDataList'], true);
                        }

                        $transientData = [];

                        if (!empty($response['loginPaymentMethodDataList'])) {

                            // Exclude some payment methods after getting textKey values of method names
                            foreach ($response['loginPaymentMethodDataList'] as $provider) {
                                if (!empty($provider['methodName'])) {
                                    if (!empty($textKeyAndFileNameMapList['PAYMENT_METHOD_' . $provider['methodName']])) {
                                        $provider['methodName'] = $textKeyAndFileNameMapList['PAYMENT_METHOD_' . $provider['methodName']];
                                        $transientData['loginPaymentMethodDataList'][$provider['methodName']] = $provider;
                                    }
                                }
                            }
                        }

                        $response = $transientData;
                        set_transient('px_api_login_deposit_methods_' . CURRENT_REGION, $transientData, (5 * 60)); // store for 5 minutes, so every 5 minutes fresh data will be fetched
                    }
                }
            }
        }

        return $response;
    }

    /**
     * Fetches navigation menu data from the API
     *
     * @return array Navigation data containing primary and secondary navigation items
     */
    public static function getNavigation()
    {
        $cachedNavigation = get_transient('px_api_navigation_' . CURRENT_REGION);

        // If cached data not found, trigger immediate fetch and return empty data for now
        if (empty($cachedNavigation)) {
            // Trigger the cron job immediately to fetch data
            self::fetchNavigationDataViaCron();

            // Get the freshly fetched data
            $cachedNavigation = get_transient('px_api_navigation_' . CURRENT_REGION);

            // If still empty, return default structure
            if (empty($cachedNavigation)) {
                $cachedNavigation = [
                    'primaryNavigation' => [],
                    'secondaryNavigation' => []
                ];
            }
        }

        return $cachedNavigation;
    }

    /**
     * Create a WordPress menu from the navigation data and set it to the mobile-menu location
     *
     * @param array $navigationData The navigation data from the API
     */
    private static function createWordPressMenuFromNavigation($navigationData)
    {
        if (empty($navigationData) || empty($navigationData['primaryNavigation']) || !is_array($navigationData['primaryNavigation'])) {
            return;
        }

        $menuName = 'Synced Menu - ' . CURRENT_REGION;
        $menu = wp_get_nav_menu_object($menuName);
        $menuId = 0;

        // If menu exists, delete it completely
        if ($menu) {
            wp_delete_nav_menu($menu->term_id);
        }

        // Create a new menu
        $menuId = wp_create_nav_menu($menuName);

        if (is_wp_error($menuId) || !$menuId) {
            return;
        }

        $position = 1;
        foreach ($navigationData['primaryNavigation'] as $item) {
            if (empty($item['label']) || empty($item['action'])) {
                continue;
            }

            wp_update_nav_menu_item($menuId, 0, [
                'menu-item-title'     => sanitize_text_field($item['label']),
                'menu-item-url'       => esc_url_raw($item['action']),
                'menu-item-status'    => 'publish',
                'menu-item-type'      => 'custom',
                'menu-item-position'  => $position++,
            ]);
        }

        // Assign menu to theme locations without overwriting others
        $locations = get_theme_mod('nav_menu_locations');
        if (!is_array($locations)) {
            $locations = [];
        }

        $locations['mobile-menu_' . CURRENT_REGION] = $menuId;

        // Optional: Uncomment if desktop menu syncing is needed
        // $locations['desktop-menu_' . CURRENT_REGION] = $menuId;

        set_theme_mod('nav_menu_locations', $locations);

        // When cache not purged, updated links won't appear, that's why we flush cache here
        if(function_exists('w3tc_flush_all')) {
            w3tc_flush_all();
        }
    }

    /**
     * Find a matching icon from available icons based on the API icon name
     *
     * @param string $apiIcon The icon name from the API
     * @param array $availableIcons Array of available icon filenames
     * @return string|null The matched icon filename or null if no match found
     */
    private static function findMatchingIcon($apiIcon, $availableIcons)
    {
        if (empty($apiIcon) || empty($availableIcons)) {
            return null;
        }

        // Clean up the API icon name and convert to singular form
        $searchTerm = strtolower(str_replace(['-', '_', ' '], '', $apiIcon));
        // Remove trailing 's' to handle plurals
        $searchTermSingular = rtrim($searchTerm, 's');

        // First try to find an exact match
        foreach ($availableIcons as $icon) {
            $cleanIcon = strtolower(str_replace(['-', '_', ' '], '', $icon));
            // Try both plural and singular forms
            if ($cleanIcon === $searchTerm || $cleanIcon === $searchTermSingular) {
                return $icon;
            }
        }

        // If no exact match, split search term into words and look for matches
        $searchWords = preg_split('/[-_\s]/', $apiIcon);
        $searchWords = array_filter($searchWords); // Remove empty elements

        foreach ($availableIcons as $icon) {
            $cleanIcon = strtolower($icon);

            // Check if any of the search words match the icon name
            foreach ($searchWords as $word) {
                $word = strtolower($word);
                // Remove 'line' suffix as it's commonly used in icon names but might not be in our icons
                $word = str_replace('line', '', $word);
                $word = trim($word);

                if (!empty($word) && strpos($cleanIcon, $word) !== false) {
                    return $icon;
                }

                // Try singular form if no match found
                $wordSingular = rtrim($word, 's');
                if (!empty($wordSingular) && $wordSingular !== $word && strpos($cleanIcon, $wordSingular) !== false) {
                    return $icon;
                }
            }
        }

        // If still no match, try partial matches with the original search term
        foreach ($availableIcons as $icon) {
            $cleanIcon = strtolower(str_replace(['-', '_', ' '], '', $icon));
            if (strpos($cleanIcon, $searchTermSingular) !== false || strpos($searchTermSingular, $cleanIcon) !== false) {
                return $icon;
            }
        }

        return null;
    }

    /**
     * Process navigation URLs to ensure they have the correct base URL
     *
     * @param array $navigationItems Array of navigation items
     * @return array Processed navigation items with correct URLs
     */
    private static function processNavigationUrls($navigationItems)
    {
        if (empty($navigationItems) || !is_array($navigationItems)) {
            return $navigationItems;
        }

        $baseUrl = brandUrl();

        foreach ($navigationItems as &$item) {
            if (!empty($item['action']) && is_string($item['action'])) {
                // Check if the URL starts with http:// or https://
                if (!preg_match('/^https?:\/\//i', $item['action'])) {
                    // If URL starts with a slash, append it to the base URL
                    if (substr($item['action'], 0, 1) === '/') {
                        $item['action'] = rtrim($baseUrl, '/') . $item['action'];
                    } else {
                        // If URL doesn't start with a slash, add one
                        $item['action'] = rtrim($baseUrl, '/') . '/' . $item['action'];
                    }
                }
            }
        }

        return $navigationItems;
    }

    /**
     * Manually refresh navigation data (useful for testing or immediate updates)
     */
    public static function refreshNavigationData()
    {
        // Delete existing transient
        delete_transient('px_api_navigation_' . CURRENT_REGION);

        // Fetch fresh data
        self::fetchNavigationDataViaCron();

        return get_transient('px_api_navigation_' . CURRENT_REGION);
    }

    public static function disableSync()
    {
        $brandConfig = get_transient('px_api_brand_' . CURRENT_REGION);
        $footerLogos = get_transient('px_api_brand_footer_logos_' . CURRENT_REGION);

        if (!empty($brandConfig)) {
            delete_transient('px_api_brand_' . CURRENT_REGION);
        }
        if (!empty($footerLogos)) {
            delete_transient('px_api_brand_footer_logos_' . CURRENT_REGION);
        }
    }
}
