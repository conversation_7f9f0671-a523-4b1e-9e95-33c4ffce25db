{"key": "group_59b2ab45699a0", "title": "Settings - <PERSON> Settings", "fields": [{"key": "field_607ea43e8b788", "label": "Extras", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_65eeebbb08649", "label": "Root Redirect", "name": "main_url_redirect", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-geoip_blocker js-main_url_redirect", "id": ""}, "message": "", "default_value": 1, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_634d5d32ff2ff", "label": "GeoIP Blocker", "name": "geoip_blocker", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-geoip_blocker", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_60bf45fd0ff13", "label": "Zendesk Support", "name": "enable_zendesk", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-enable_zendesk", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_64e4831854cca", "label": "ADA Chatbot", "name": "ada_support", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-ada_support", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_624ac7bea1f2f", "label": "Quick Register/Login", "name": "enable_quick_register_login", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-enable_quick_register_login", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_jbtpo543mo5c2y", "label": "Disable Mobile Nav Sync", "name": "disable_mobile_nav_sync", "aria-label": "", "type": "true_false", "instructions": "If you activate this, you can use 'Menus' page on admin to manage the mobile navigation menu links manually.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "js-disable_mobile_nav_sync", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_650cc606c063c", "label": "<PERSON><PERSON>", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_65f9b856f9730", "label": "Disable <PERSON><PERSON>", "name": "disable_cookie_banner", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-disable_cookie_banner", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_6231d75558850", "label": "<PERSON><PERSON> - Position", "name": "cookie_consent_position", "aria-label": "", "type": "button_group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-cookie_consent_position", "id": ""}, "choices": {"top": "Top", "bottom": "Bottom"}, "default_value": "", "return_format": "value", "allow_null": 0, "layout": "vertical"}, {"key": "field_625418e2afd13", "label": "<PERSON><PERSON> Bar - Color", "name": "cookie_consent_color", "aria-label": "", "type": "button_group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-cookie_consent_color", "id": ""}, "choices": {"info": "Normal", "dark": "Dark"}, "default_value": "", "return_format": "value", "allow_null": 0, "layout": "horizontal"}, {"key": "field_5fae9616e3daa", "label": "Header", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5c7d2e849baad", "label": "Compliance Header", "name": "compliance_header", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-compliance_header", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_5e5f8d48b06b8", "label": "Header CTA Button", "name": "custom_header_cta", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-custom_header_cta", "id": ""}, "message": "Use custom Header CTA Button?", "default_value": 0, "ui_on_text": "Custom", "ui_off_text": "<PERSON><PERSON><PERSON>", "ui": 1}, {"key": "field_5e5f98437c48c", "label": "Header CTA Priority", "name": "cta_priority", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33.33", "class": "js-cta_priority", "id": ""}, "message": "Always Show CTA even when user is logged in", "default_value": 0, "ui_on_text": "Always show CTA", "ui_off_text": "Default (logout link)", "ui": 1}, {"key": "field_5e5f8d6db06b9", "label": "", "name": "", "aria-label": "", "type": "clone", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5e5f8d48b06b8", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "js-cta_group", "id": ""}, "clone": ["field_5db19cc371eeb"], "display": "group", "layout": "block", "prefix_label": 0, "prefix_name": 0}, {"key": "field_607ea79610c28", "label": "Casino Info Top Bar (Start Page)", "name": "casino_info", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-casino_info", "id": ""}, "collapsed": "", "min": 0, "max": 3, "layout": "block", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_607ea4768b789", "label": "Label and Value", "name": "label_and_value", "aria-label": "", "type": "clone", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-label_and_value", "id": ""}, "clone": ["group_5f741fff1fadf"], "display": "seamless", "layout": "block", "prefix_label": 0, "prefix_name": 0, "parent_repeater": "field_607ea79610c28"}]}, {"key": "field_5ee871306a994", "label": "Social Networks", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5ee8713f6a995", "label": "", "name": "social_media_icons", "aria-label": "", "type": "clone", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "js-social_media_icons", "id": ""}, "clone": ["group_5ee3536907013"], "display": "seamless", "layout": "block", "prefix_label": 0, "prefix_name": 0}, {"key": "field_61e68c7a14d6e", "label": "Mobile Apps", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_61e68c8914d6f", "label": "Apple App Store", "name": "appstore", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-appstore", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_61e68c9c14d70", "label": "Android Play Store", "name": "android", "aria-label": "", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "js-android", "id": ""}, "default_value": "", "placeholder": ""}], "location": [[{"param": "options_page", "operator": "==", "value": "brand-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 1, "modified": 1750147213}