<?php
// Client browser check
function isFirefox() {
    return (str_contains((string) $_SERVER['HTTP_USER_AGENT'], 'Firefox'));
}

function isSafari() {
    return (str_contains( (string) $_SERVER['HTTP_USER_AGENT'], 'Safari') && !str_contains( (string) $_SERVER['HTTP_USER_AGENT'], 'Chrome'));
}

function isExplorer() {
    return (strpos((string) $_SERVER['HTTP_USER_AGENT'], 'msie') || str_contains((string) $_SERVER['HTTP_USER_AGENT'], 'Trident/7'));
}

// Custom admin bar
function custom_admin_bar($admin_bar) {
    global $current_user;
    wp_get_current_user();

    // Phoenix Toolkit
    if (current_user_can('administrator')) {
        // Check if API Base URL is overridden
        $api_base_override = get_transient('px_api_base_override');

        // Phoenix Toolkit menu
        $admin_bar->add_menu([
            'id' => 'phoenix-toolkit',
            'title' => '🔥 Phoenix Toolkit' . ($api_base_override ? ' <span style="color: #fff;">(API Override)</span>' : ''),
            'href' => '#',
            'meta' => [
                'title' => __('Toolkit'),
                'class' => ((DEBUG_MODE || SIMULATED_USER || SIMULATED_TIME || SIMULATED_SITE_TYPE || $api_base_override) ? 'active' : '')
            ]
        ]);

        include('toolkit/service-status.php');
        include('toolkit/quick-links.php');
        include('toolkit/debug-mode.php');
        include('toolkit/user-simulator.php');
        include('toolkit/time-simulator.php');
        include('toolkit/site-type-simulator.php');
        include('toolkit/caches.php');
        include('toolkit/clean-db-games.php');
        include('toolkit/clean-db-deprecated.php');
        include('toolkit/clean-deprecated-templates.php');
        include('toolkit/clean-old-campaigns.php');
        include('toolkit/fix-expired-content.php');
        include('toolkit/refresh-navigation.php');

        /*
            Fix terms is removing and setting terms with up-to-date terms.
            The purpose is to clear outdated/deprecated/old terms.
            During the removal procedure, all published posts lose their terms.
            Which brings the necessity to edit and set correct template for each post.
            This is a time-consuming process and should be used with caution on prod.
            Therefore I'm commenting it out for now.
        */
        // include('toolkit/fix-terms.php');
        include('toolkit/api-base-override.php');
    }

    // ACF Timezone helper
    if (function_exists('icl_object_id')) {
        $currentLang = wpml_get_current_language();
        $admin_bar->add_menu([
            'id' => 'timezone',
            'title' => '<span class="timezone-' . $currentLang . '"><span id="location-' . $currentLang . '"></span> - <span id="current-time-' . $currentLang . '"></span></span>',
            'href' => '#'
        ]);

        $languages = array_keys(icl_get_languages());
        $languages = array_filter($languages, fn($language) => $language != $currentLang);

        foreach ($languages as $language) {
            $admin_bar->add_menu([
                'id' => 'timezone-' . $language,
                'parent' => 'timezone',
                'title' => '<span class="timezone-' . $language . '"><span id="location-' . $language . '"></span> - <span id="current-time-' . $language . '"></span></span>',
                'href' => '#'
            ]);
        }
    }
}
add_action('admin_bar_menu', 'custom_admin_bar', 101);

// Custom brand logo in admin bar
add_action('admin_head', 'custom_brand_logo', 100);
function custom_brand_logo() {
    $logo_path = (file_exists(get_stylesheet_directory_uri() . "/dist/images/logo.png") ? get_stylesheet_directory_uri() . "/dist/images/logo.png" : get_stylesheet_directory_uri() . "/vectors/logos/logo.svg");
    echo "<style>.wp-admin #wpadminbar #wp-admin-bar-site-name>.ab-item:before{
    background-image:url('" . $logo_path . "'),url('" . $logo_path . "')!important;}</style>";
}