<?php
// Refresh Navigation Data - Manually trigger navigation data refresh

// Handle the refresh navigation action
if (isset($_GET['refresh_navigation_data'])) {
    // Call the BrandAPI method to refresh navigation data
    if (class_exists('BrandAPI')) {
        $result = BrandAPI::refreshNavigationData();

        // Show success message
        add_action('admin_notices', function () use ($result) {
            $message = '🔥 Navigation data refreshed successfully!';
            if (!empty($result['primaryNavigation'])) {
                $count = count($result['primaryNavigation']);
                $message .= " Found {$count} navigation items.";
            }
            echo '<div class="notice notice-success settings-error is-dismissible"><p>' . $message . '</p></div>';
        });
    } else {
        add_action('admin_notices', function () {
            echo '<div class="notice notice-error settings-error is-dismissible"><p>❌ BrandAPI class not found!</p></div>';
        });
    }
}

// Add the Refresh Navigation menu item
$admin_bar->add_node([
    'parent' => 'phoenix-toolkit',
    'id' => 'refresh_navigation_data',
    'title' => '<span class="ab-icon dashicons dashicons-update"></span> Refresh Navigation',
    'href' => '#',
    'meta' => [
        'title' => __('Refresh Navigation Data'),
        'onclick' => 'toolkitConfirm("This will refresh the navigation data from the API and update WordPress menus. Are you sure?", {"refresh_navigation_data": "1"});'
    ]
]);
