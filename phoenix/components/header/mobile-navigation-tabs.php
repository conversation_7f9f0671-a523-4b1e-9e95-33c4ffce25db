<?php
if (function_exists('get_transient')) {
    $mobileMenu = get_transient('px_api_navigation_' . CURRENT_REGION);
}
?>
<div class="navigation navigation--mobile-tabs">
    <div class="navigation__menu navigation__menu--mobile-tabs">
        <ul class="menu">
            <?php
            if (!empty($mobileMenu) && (!get_field_tweaked('disable_mobile_nav_sync', 'option'))) {
                foreach ($mobileMenu['primaryNavigation'] as $item) {
                    if (!empty($item['action']) && !empty($item['label'])) {
                        echo '<li><a href="' . $item['action'] . '" data-gtm-key="' . $item['key'] . '">' . $item['label'] . '</a></li>';
                    }
                }
            } else {
                if (empty($args['location'])) {
                    $args['location'] = 'mobile-menu_' . CURRENT_REGION;
                }

                if (has_nav_menu($args['location'])) {
                    wp_nav_menu([
                        'theme_location' => $args['location'],
                        'walker' => new Phnx_Walker_Nav_Menu(),
                        'sort_column' => 'menu_order',
                    ]);
                }
            }
            ?>
        </ul>
    </div>
</div>
